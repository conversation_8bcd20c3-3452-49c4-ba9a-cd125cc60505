# Kode API 文档

Kode API 提供了HTTP接口来访问AI助手的功能，支持普通请求和SSE流式响应。

## 启动API服务器

```bash
# 使用默认配置启动
kode api

# 自定义配置启动
kode api --port 8080 --host 127.0.0.1 --cwd /path/to/project

# 启用安全模式和架构工具
kode api --safe --enable-architect
```

### 命令选项

- `--port, -p <port>` - 监听端口 (默认: 3000)
- `--host, -h <host>` - 绑定主机 (默认: 0.0.0.0)
- `--cwd, -c <cwd>` - 工作目录 (默认: 当前目录)
- `--enable-architect, -e` - 启用架构工具
- `--safe` - 启用安全模式
- `--verbose` - 启用详细模式

## API 端点

### 1. 健康检查

**GET** `/api/health`

检查API服务器状态。

**响应示例:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "version": "1.0.0"
}
```

### 2. 启动服务设置

**POST** `/api/start`

设置工作环境和环境变量。

**请求体:**
```json
{
  "cwd": "/path/to/project",
  "env": {
    "DEBUG": "true",
    "NODE_ENV": "development"
  }
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "服务启动成功",
  "cwd": "/path/to/project"
}
```

### 3. 普通对话

**POST** `/api/chat`

发送消息并获取AI响应。

**请求体:**
```json
{
  "message": "你好，请介绍一下当前项目",
  "cwd": "/optional/working/directory",
  "enableArchitect": false,
  "safeMode": false,
  "sessionId": "optional-session-id"
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "response": "这是一个Kode AI助手项目...",
    "sessionId": "session_1234567890_abc123def"
  }
}
```

**错误响应:**
```json
{
  "success": false,
  "error": "错误信息"
}
```

### 4. SSE流式对话

**POST** `/api/chat/stream`

发送消息并通过Server-Sent Events接收流式响应。

**请求体:** (与普通对话相同)
```json
{
  "message": "请分析当前项目结构",
  "sessionId": "stream-session-1"
}
```

**SSE事件流:**

```
event: start
data: {"sessionId": "session_1234567890_abc123def"}

event: chunk
data: {"data": "正在分析项目结构..."}

event: chunk
data: {"data": "发现以下文件和目录:"}

event: complete
data: {"response": "完整的分析结果..."}

event: end
data: {}
```

**错误事件:**
```
event: error
data: {"error": "错误信息"}
```

## 使用示例

### JavaScript/Node.js

```javascript
// 普通对话
const response = await fetch('http://localhost:3000/api/chat', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    message: '你好，Kode!'
  })
})
const result = await response.json()
console.log(result.data.response)

// SSE流式对话
const sseResponse = await fetch('http://localhost:3000/api/chat/stream', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    message: '请帮我分析代码'
  })
})

const reader = sseResponse.body.getReader()
const decoder = new TextDecoder()

while (true) {
  const { done, value } = await reader.read()
  if (done) break
  
  const chunk = decoder.decode(value)
  // 处理SSE数据
  console.log(chunk)
}
```

### Python

```python
import requests
import json

# 普通对话
def chat(message):
    response = requests.post('http://localhost:3000/api/chat', 
        json={'message': message})
    return response.json()

result = chat('你好，请介绍一下Kode项目')
print(result['data']['response'])

# SSE流式对话
def chat_stream(message):
    response = requests.post('http://localhost:3000/api/chat/stream',
        json={'message': message}, stream=True)
    
    for line in response.iter_lines():
        if line:
            line_str = line.decode('utf-8')
            if line_str.startswith('data: '):
                data = json.loads(line_str[6:])
                yield data

for chunk in chat_stream('分析当前项目'):
    print(chunk)
```

### cURL

```bash
# 健康检查
curl http://localhost:3000/api/health

# 普通对话
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "你好，Kode!"}'

# SSE流式对话
curl -X POST http://localhost:3000/api/chat/stream \
  -H "Content-Type: application/json" \
  -d '{"message": "请分析项目结构"}' \
  --no-buffer
```

## 注意事项

1. **CORS**: API服务器启用了CORS，允许跨域请求
2. **错误处理**: 所有端点都会返回适当的HTTP状态码和错误信息
3. **会话管理**: sessionId是可选的，用于跟踪对话会话
4. **工作目录**: 可以为每个请求指定不同的工作目录
5. **环境变量**: 通过`/api/start`端点设置的环境变量会影响后续的AI处理

## 扩展功能

API设计支持未来扩展，包括：
- 文件上传和处理
- 实时协作功能
- 更多的AI工具和模型选择
- 会话持久化和恢复