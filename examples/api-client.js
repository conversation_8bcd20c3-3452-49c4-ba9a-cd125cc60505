#!/usr/bin/env node

/**
 * Kode API 客户端示例
 * 
 * 使用方法:
 * node examples/api-client.js
 */

const API_BASE = 'http://localhost:3000/api'

// 普通HTTP请求示例
async function httpChatExample() {
  console.log('🔄 HTTP对话示例')
  
  try {
    // 1. 启动服务设置
    const startResponse = await fetch(`${API_BASE}/start`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        cwd: "/Users/<USER>/code/xcoding/test3",
        env: {
          DEBUG: 'true'
        }
      })
    })
    
    const startResult = await startResponse.json()
    console.log('启动结果:', startResult)

    // 2. 发送对话
    const chatResponse = await fetch(`${API_BASE}/chat`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: '你好，请介绍一下当前项目的结构',
        sessionId: 'example-session-1'
      })
    })

    const chatResult = await chatResponse.json()
    console.log('对话结果:', chatResult)
    
  } catch (error) {
    console.error('HTTP请求失败:', error)
  }
}

// SSE流式对话示例
async function sseStreamExample() {
  console.log('\n🌊 SSE流式对话示例')
  
  try {
    const response = await fetch(`${API_BASE}/chat/stream`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: '请分析当前目录中的package.json文件',
        sessionId: 'example-session-2'
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    console.log('开始接收流式响应...')
    
    const reader = response.body?.getReader()
    const decoder = new TextDecoder()

    if (!reader) {
      throw new Error('无法获取响应流')
    }

    while (true) {
      const { done, value } = await reader.read()
      
      if (done) {
        console.log('\n✅ 流式响应完成')
        break
      }

      const chunk = decoder.decode(value)
      const lines = chunk.split('\n')

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6))
            console.log('📦 接收到数据:', data)
          } catch (e) {
            // 忽略解析错误
          }
        } else if (line.startsWith('event: ')) {
          const event = line.slice(7)
          console.log('🎉 事件:', event)

          // 特殊处理状态事件
          if (event === 'status' || event === 'status_update') {
            // 状态事件会在下一行的data中包含状态信息
            continue
          }
        }
      }
    }
    
  } catch (error) {
    console.error('SSE请求失败:', error)
  }
}

// 健康检查示例
async function healthCheckExample() {
  console.log('\n💊 健康检查示例')
  
  try {
    const response = await fetch(`${API_BASE}/health`)
    const result = await response.json()
    console.log('健康状态:', result)
  } catch (error) {
    console.error('健康检查失败:', error)
  }
}

// Node.js环境检查
function checkNodeEnvironment() {
  if (typeof fetch === 'undefined') {
    console.error('❌ 此示例需要Node.js 18+或安装node-fetch包')
    console.log('请升级Node.js版本或运行: npm install node-fetch')
    process.exit(1)
  }
}

// 主函数
async function main() {
  console.log('🚀 Kode API 客户端示例\n')
  
  checkNodeEnvironment()
  
  // 检查API服务是否运行
  try {
    await fetch(`${API_BASE}/health`)
  } catch (error) {
    console.error('❌ 无法连接到API服务器')
    console.log('请先启动API服务器: bun run dev -- api')
    console.log('或者: kode api')
    process.exit(1)
  }

  // 运行示例
  await healthCheckExample()
  await httpChatExample()
  await sseStreamExample()
  
  console.log('\n✨ 所有示例完成!')
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(error => {
    console.error('示例运行失败:', error)
    process.exit(1)
  })
}

module.exports = {
  httpChatExample,
  sseStreamExample,
  healthCheckExample
}