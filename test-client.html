<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kode API 测试客户端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            background: #0d1117;
            color: #c9d1d9;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #58a6ff;
            margin-bottom: 10px;
        }

        .status-panel {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .status-row {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .status-row:last-child {
            margin-bottom: 0;
        }

        .status-label {
            width: 120px;
            color: #7d8590;
            font-weight: bold;
        }

        .status-input {
            flex: 1;
            background: #0d1117;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 8px 12px;
            color: #c9d1d9;
            font-family: inherit;
            margin-right: 10px;
        }

        .status-input:focus {
            outline: none;
            border-color: #58a6ff;
        }

        .btn {
            background: #238636;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            cursor: pointer;
            font-family: inherit;
            font-size: 14px;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #2ea043;
        }

        .btn:disabled {
            background: #484f58;
            cursor: not-allowed;
        }

        .btn-danger {
            background: #da3633;
        }

        .btn-danger:hover {
            background: #f85149;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-ready {
            background: #1a7f37;
            color: white;
        }

        .status-working {
            background: #bf8700;
            color: white;
        }

        .status-error {
            background: #da3633;
            color: white;
        }

        .chat-container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            height: 600px;
        }

        .chat-output {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 8px;
            padding: 20px;
            overflow-y: auto;
            font-size: 14px;
        }

        .chat-input-container {
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 8px;
            padding: 20px;
        }

        .chat-input {
            width: 100%;
            background: #0d1117;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 12px;
            color: #c9d1d9;
            font-family: inherit;
            font-size: 14px;
            resize: vertical;
            min-height: 80px;
        }

        .chat-input:focus {
            outline: none;
            border-color: #58a6ff;
        }

        .chat-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 6px;
            border-left: 4px solid #30363d;
        }

        .message-user {
            background: #0d1117;
            border-left-color: #58a6ff;
        }

        .message-assistant {
            background: #0f1419;
            border-left-color: #238636;
        }

        .message-status {
            background: #1c1e24;
            border-left-color: #bf8700;
            font-style: italic;
        }

        .message-tool {
            background: #1a1d23;
            border-left-color: #7c3aed;
            font-size: 12px;
        }

        .message-error {
            background: #2d1b1b;
            border-left-color: #da3633;
        }

        .timestamp {
            color: #7d8590;
            font-size: 11px;
            margin-bottom: 5px;
        }

        .spinner {
            display: inline-block;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .tool-call {
            background: #1a1d23;
            border: 1px solid #30363d;
            border-radius: 4px;
            padding: 8px;
            margin: 5px 0;
            font-size: 12px;
        }

        .tool-name {
            color: #7c3aed;
            font-weight: bold;
        }

        .tool-input {
            color: #7d8590;
            margin-top: 4px;
        }

        .clear-btn {
            background: #6e7681;
            color: white;
        }

        .clear-btn:hover {
            background: #7d8590;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Kode API 测试客户端</h1>
            <p>连接到 localhost:3000 进行测试</p>
        </div>

        <!-- 状态面板 -->
        <div class="status-panel">
            <div class="status-row">
                <div class="status-label">项目路径:</div>
                <input type="text" id="projectPath" class="status-input" 
                       placeholder="/Users/<USER>/code/xcoding/test3" 
                       value="/Users/<USER>/code/xcoding/test3">
                <button id="startBtn" class="btn">启动项目</button>
                <button id="stopBtn" class="btn btn-danger" disabled>停止</button>
            </div>
            <div class="status-row">
                <div class="status-label">当前状态:</div>
                <div id="statusIndicator" class="status-indicator status-ready">就绪</div>
                <div id="statusMessage" style="margin-left: 15px; color: #7d8590;"></div>
            </div>
        </div>

        <!-- 聊天界面 -->
        <div class="chat-container">
            <div id="chatOutput" class="chat-output">
                <div class="message message-assistant">
                    <div class="timestamp">系统消息</div>
                    <div>欢迎使用 Kode API 测试客户端！请先设置项目路径并启动项目。</div>
                </div>
            </div>
            
            <div class="chat-input-container">
                <textarea id="chatInput" class="chat-input" 
                          placeholder="输入你的命令或问题...&#10;例如：分析当前项目结构&#10;例如：创建一个新的React组件"></textarea>
                <div class="chat-controls">
                    <div>
                        <button id="sendBtn" class="btn" disabled>发送消息</button>
                        <button id="clearBtn" class="clear-btn btn">清空对话</button>
                        <button id="historyBtn" class="clear-btn btn">查看历史</button>
                    </div>
                    <div id="chatStatus" style="color: #7d8590; font-size: 12px;"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class KodeAPIClient {
            constructor() {
                this.baseURL = 'http://localhost:3000';
                this.isProjectStarted = false;
                this.currentEventSource = null;
                this.currentStatus = '';
                this.statusStartTime = 0;
                this.statusTimer = null;
                this.spinnerTimer = null;
                this.currentSpinnerIndex = 0;
                this.sessionId = null; // 保持会话ID

                // 动画字符（完全对应 Spinner.tsx）
                const baseChars = ['·', '✢', '✳', '∗', '✻', '✽'];
                this.spinnerFrames = [...baseChars, ...[...baseChars].reverse()]; // 来回动画

                // 状态消息（完全对应 Spinner.tsx）
                this.statusMessages = [
                    'Accomplishing', 'Actioning', 'Actualizing', 'Baking', 'Brewing',
                    'Calculating', 'Cerebrating', 'Churning', 'Coding', 'Coalescing',
                    'Cogitating', 'Computing', 'Conjuring', 'Considering', 'Cooking',
                    'Crafting', 'Creating', 'Crunching', 'Deliberating', 'Determining',
                    'Doing', 'Effecting', 'Finagling', 'Forging', 'Forming',
                    'Generating', 'Hatching', 'Herding', 'Honking', 'Hustling',
                    'Ideating', 'Inferring', 'Manifesting', 'Marinating', 'Moseying',
                    'Mulling', 'Mustering', 'Musing', 'Noodling', 'Percolating',
                    'Pondering', 'Processing', 'Puttering', 'Reticulating', 'Ruminating',
                    'Schlepping', 'Shucking', 'Simmering', 'Smooshing', 'Spinning',
                    'Stewing', 'Synthesizing', 'Thinking', 'Transmuting', 'Vibing', 'Working'
                ];

                this.initElements();
                this.bindEvents();
                this.checkHealth();
            }

            initElements() {
                this.projectPathInput = document.getElementById('projectPath');
                this.startBtn = document.getElementById('startBtn');
                this.stopBtn = document.getElementById('stopBtn');
                this.statusIndicator = document.getElementById('statusIndicator');
                this.statusMessage = document.getElementById('statusMessage');
                this.chatOutput = document.getElementById('chatOutput');
                this.chatInput = document.getElementById('chatInput');
                this.sendBtn = document.getElementById('sendBtn');
                this.clearBtn = document.getElementById('clearBtn');
                this.historyBtn = document.getElementById('historyBtn');
                this.chatStatus = document.getElementById('chatStatus');
            }

            bindEvents() {
                this.startBtn.addEventListener('click', () => this.startProject());
                this.stopBtn.addEventListener('click', () => this.stopProject());
                this.sendBtn.addEventListener('click', () => this.sendMessage());
                this.clearBtn.addEventListener('click', () => this.clearChat());
                this.historyBtn.addEventListener('click', () => this.showHistory());
                
                this.chatInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });
            }

            async checkHealth() {
                try {
                    const response = await fetch(`${this.baseURL}/api/health`);
                    const data = await response.json();
                    this.updateStatus('ready', `API 服务正常 (v${data.version})`);
                } catch (error) {
                    this.updateStatus('error', 'API 服务连接失败');
                }
            }

            async startProject() {
                const path = this.projectPathInput.value.trim();
                if (!path) {
                    alert('请输入项目路径');
                    return;
                }

                try {
                    this.updateStatus('working', '正在启动项目...');
                    this.startBtn.disabled = true;

                    const response = await fetch(`${this.baseURL}/api/start`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ cwd: path })
                    });

                    const data = await response.json();
                    
                    if (data.success) {
                        this.isProjectStarted = true;
                        this.updateStatus('ready', `项目已启动: ${data.cwd}`);
                        this.startBtn.disabled = true;
                        this.stopBtn.disabled = false;
                        this.sendBtn.disabled = false;
                        this.addMessage('system', `✅ 项目启动成功！工作目录: ${data.cwd}`);
                    } else {
                        throw new Error(data.error || '启动失败');
                    }
                } catch (error) {
                    this.updateStatus('error', `启动失败: ${error.message}`);
                    this.startBtn.disabled = false;
                    this.addMessage('error', `❌ 项目启动失败: ${error.message}`);
                }
            }

            async stopProject() {
                try {
                    this.updateStatus('working', '正在停止项目...');
                    this.stopBtn.disabled = true;

                    // 重置连接状态
                    this.currentEventSource = null;

                    // 发送停止请求到服务器
                    const response = await fetch(`${this.baseURL}/api/stop`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({})
                    });

                    const data = await response.json();

                    if (data.success) {
                        this.isProjectStarted = false;
                        this.updateStatus('ready', `项目已停止: ${data.cwd}`);
                        this.startBtn.disabled = false;
                        this.stopBtn.disabled = true;
                        this.sendBtn.disabled = true;
                        this.clearStatusTimer();
                        this.addMessage('system', `🛑 项目已停止！工作目录重置为: ${data.cwd}`);
                    } else {
                        throw new Error(data.error || '停止失败');
                    }
                } catch (error) {
                    this.updateStatus('error', `停止失败: ${error.message}`);
                    this.stopBtn.disabled = false;
                    this.addMessage('error', `❌ 项目停止失败: ${error.message}`);
                }
            }

            async sendMessage() {
                const message = this.chatInput.value.trim();
                if (!message || !this.isProjectStarted) return;

                // 添加用户消息
                this.addMessage('user', message);
                this.chatInput.value = '';
                this.sendBtn.disabled = true;

                try {
                    // 开始 SSE 连接
                    this.startSSEConnection(message);
                } catch (error) {
                    this.addMessage('error', `❌ 发送失败: ${error.message}`);
                    this.sendBtn.disabled = false;
                }
            }

            async startSSEConnection(message) {
                try {
                    // 使用 fetch 发送 POST 请求并处理 SSE 流
                    const requestBody = {
                        message,
                        sessionId: this.sessionId // 保持会话ID
                    };

                    const response = await fetch(`${this.baseURL}/api/chat/stream`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'text/event-stream',
                            'Cache-Control': 'no-cache'
                        },
                        body: JSON.stringify(requestBody)
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    if (!response.body) {
                        throw new Error('无法获取响应流');
                    }

                    // 处理流式响应
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let buffer = '';
                    let currentEventType = '';

                    this.chatStatus.textContent = '连接已建立，等待响应...';

                    // 实时处理流式数据
                    const processStream = async () => {
                        while (true) {
                            const { done, value } = await reader.read();

                            if (done) {
                                this.sendBtn.disabled = false;
                                this.chatStatus.textContent = '';
                                break;
                            }

                            // 将新数据添加到缓冲区
                            buffer += decoder.decode(value, { stream: true });

                            // 按行处理数据
                            const lines = buffer.split('\n');
                            buffer = lines.pop() || ''; // 保留最后一个不完整的行

                            for (const line of lines) {
                                if (line.trim() === '') continue;

                                if (line.startsWith('event: ')) {
                                    currentEventType = line.slice(7).trim();
                                } else if (line.startsWith('data: ')) {
                                    try {
                                        const data = JSON.parse(line.slice(6));
                                        // 立即处理每个事件
                                        await this.handleSSEEvent(currentEventType, data);
                                        // 强制重绘以确保立即显示
                                        await new Promise(resolve => requestAnimationFrame(resolve));
                                    } catch (e) {
                                        console.warn('解析 SSE 数据失败:', line, e);
                                    }
                                }
                            }
                        }
                    };

                    await processStream();

                } catch (error) {
                    this.addMessage('error', `❌ 连接错误: ${error.message}`);
                    this.sendBtn.disabled = false;
                    this.updateStatus('error', '连接错误');
                    this.clearStatusTimer();
                }
            }

            async handleSSEEvent(eventType, data) {
                console.log('SSE Event:', eventType, data); // 调试信息

                switch (eventType) {
                    case 'start':
                        // 保存会话ID以便后续请求使用
                        this.sessionId = data.sessionId;
                        this.chatStatus.textContent = `会话开始 (${data.sessionId})`;
                        this.addMessage('system', `🚀 开始处理: ${data.sessionId}`);
                        break;

                    case 'status':
                        console.log('Setting status:', data.message); // 调试信息
                        this.currentStatus = data.message;
                        this.statusStartTime = data.startTime;
                        this.currentSpinnerIndex = 0; // 重置动画索引
                        const initialSpinner = this.spinnerFrames[0];
                        // 完全对应终端格式
                        const statusText = `${initialSpinner} ${data.message}… (0s · esc to interrupt)`;
                        this.updateStatus('working', statusText);
                        this.startStatusTimer();
                        this.startSpinnerAnimation();
                        // 添加状态消息到聊天区域
                        this.addMessage('status', statusText);
                        break;

                    case 'status_update':
                        // 状态更新由动画定时器处理，这里不需要额外处理
                        // 因为我们已经有了本地的动画和计时器
                        console.log('Status update:', data.message, data.elapsedTime); // 调试信息
                        break;

                    case 'tool_call':
                        console.log('处理工具调用:', data); // 调试信息
                        this.addToolCall(data);
                        break;

                    case 'tool_result':
                        console.log('处理工具结果:', data); // 调试信息
                        this.addToolResult(data);
                        break;

                    case 'chunk':
                        this.addOrUpdateAssistantMessage(data.data);
                        break;

                    case 'complete':
                        this.chatStatus.textContent = '响应完成';
                        this.updateStatus('ready', '就绪');
                        this.clearAllTimers(); // 清理所有定时器
                        this.addMessage('system', '✅ 响应完成');
                        break;

                    case 'end':
                        this.sendBtn.disabled = false;
                        this.chatStatus.textContent = '';
                        break;
                }

                // 确保 UI 立即更新
                await new Promise(resolve => setTimeout(resolve, 0));
            }



            addMessage(type, content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message message-${type}`;
                messageDiv.setAttribute('data-message-type', type);

                const timestamp = document.createElement('div');
                timestamp.className = 'timestamp';
                timestamp.textContent = new Date().toLocaleTimeString();

                const contentDiv = document.createElement('div');
                contentDiv.textContent = content;

                messageDiv.appendChild(timestamp);
                messageDiv.appendChild(contentDiv);
                this.chatOutput.appendChild(messageDiv);
                this.chatOutput.scrollTop = this.chatOutput.scrollHeight;
            }

            updateLastStatusMessage(content) {
                // 查找最后一个状态消息并更新它
                const statusMessages = this.chatOutput.querySelectorAll('.message-status');
                if (statusMessages.length > 0) {
                    const lastStatusMessage = statusMessages[statusMessages.length - 1];
                    const contentDiv = lastStatusMessage.querySelector('div:last-child');
                    if (contentDiv) {
                        contentDiv.textContent = content;
                    }
                }
            }

            addOrUpdateAssistantMessage(content) {
                let lastMessage = this.chatOutput.lastElementChild;

                // 检查是否需要创建新的助手消息
                if (!lastMessage ||
                    !lastMessage.classList.contains('message-assistant') ||
                    lastMessage.querySelector('.tool-call') ||
                    lastMessage.getAttribute('data-message-type') !== 'assistant') {

                    // 创建新的助手消息
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'message message-assistant';
                    messageDiv.setAttribute('data-message-type', 'assistant');

                    const timestamp = document.createElement('div');
                    timestamp.className = 'timestamp';
                    timestamp.textContent = new Date().toLocaleTimeString();

                    const contentDiv = document.createElement('div');
                    contentDiv.innerHTML = this.formatContent(content);

                    messageDiv.appendChild(timestamp);
                    messageDiv.appendChild(contentDiv);
                    this.chatOutput.appendChild(messageDiv);
                } else {
                    // 更新现有消息
                    const contentDiv = lastMessage.querySelector('div:last-child');
                    if (contentDiv) {
                        contentDiv.innerHTML += this.formatContent(content);
                    }
                }

                // 立即滚动到底部
                this.chatOutput.scrollTop = this.chatOutput.scrollHeight;
            }

            addToolCall(data) {
                console.log('addToolCall 被调用:', data); // 调试信息
                const toolDiv = document.createElement('div');
                toolDiv.className = 'tool-call';
                toolDiv.innerHTML = `
                    <div class="tool-name">🔧 ${data.tool}</div>
                    <div class="tool-input">${JSON.stringify(data.input, null, 2)}</div>
                `;

                const messageDiv = document.createElement('div');
                messageDiv.className = 'message message-tool';

                const timestamp = document.createElement('div');
                timestamp.className = 'timestamp';
                timestamp.textContent = new Date().toLocaleTimeString();

                messageDiv.appendChild(timestamp);
                messageDiv.appendChild(toolDiv);
                this.chatOutput.appendChild(messageDiv);
                this.chatOutput.scrollTop = this.chatOutput.scrollHeight;
                console.log('工具调用已添加到DOM'); // 调试信息
            }

            addToolResult(data) {
                const resultDiv = document.createElement('div');
                resultDiv.className = 'tool-call';
                resultDiv.style.background = data.is_error ? '#2d1b1b' : '#1a2e1a';
                resultDiv.innerHTML = `
                    <div class="tool-name">${data.is_error ? '❌' : '✅'} 工具结果</div>
                    <div class="tool-input">${this.truncateContent(data.content, 500)}</div>
                `;

                const messageDiv = document.createElement('div');
                messageDiv.className = 'message message-tool';

                const timestamp = document.createElement('div');
                timestamp.className = 'timestamp';
                timestamp.textContent = new Date().toLocaleTimeString();

                messageDiv.appendChild(timestamp);
                messageDiv.appendChild(resultDiv);
                this.chatOutput.appendChild(messageDiv);
                this.chatOutput.scrollTop = this.chatOutput.scrollHeight;
            }

            formatContent(content) {
                return content
                    .replace(/\n/g, '<br>')
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/`(.*?)`/g, '<code style="background: #30363d; padding: 2px 4px; border-radius: 3px;">$1</code>');
            }

            truncateContent(content, maxLength) {
                if (content.length <= maxLength) return content;
                return content.substring(0, maxLength) + '...';
            }

            updateStatus(type, message) {
                console.log('Updating status:', type, message); // 调试信息
                this.statusIndicator.className = `status-indicator status-${type}`;
                this.statusIndicator.textContent = type === 'ready' ? '就绪' :
                                                  type === 'working' ? '工作中' : '错误';
                this.statusMessage.textContent = message;
                console.log('Status updated. Indicator:', this.statusIndicator.textContent, 'Message:', this.statusMessage.textContent); // 调试信息
            }

            startStatusTimer() {
                this.clearStatusTimer();
                this.statusTimer = setInterval(() => {
                    if (this.statusStartTime) {
                        const elapsed = Math.floor((Date.now() - this.statusStartTime) / 1000);
                        const spinnerChar = this.spinnerChars[this.currentSpinnerIndex];
                        this.updateStatus('working', `${spinnerChar} ${this.currentStatus}... (${elapsed}s)`);
                    }
                }, 1000);
            }

            startSpinnerAnimation() {
                this.clearSpinnerAnimation();
                this.spinnerTimer = setInterval(() => {
                    this.currentSpinnerIndex = (this.currentSpinnerIndex + 1) % this.spinnerFrames.length;
                    if (this.statusStartTime) {
                        const elapsed = Math.floor((Date.now() - this.statusStartTime) / 1000);
                        const spinnerChar = this.spinnerFrames[this.currentSpinnerIndex];
                        // 完全对应终端格式：{动画字符} {消息}… ({时间}s · esc to interrupt)
                        const statusText = `${spinnerChar} ${this.currentStatus}… (${elapsed}s · esc to interrupt)`;
                        this.updateStatus('working', statusText);
                        this.updateLastStatusMessage(statusText);
                    }
                }, 120); // 每120ms切换一次，完全对应 Spinner.tsx
            }

            clearStatusTimer() {
                if (this.statusTimer) {
                    clearInterval(this.statusTimer);
                    this.statusTimer = null;
                }
            }

            clearSpinnerAnimation() {
                if (this.spinnerTimer) {
                    clearInterval(this.spinnerTimer);
                    this.spinnerTimer = null;
                }
            }

            clearAllTimers() {
                this.clearStatusTimer();
                this.clearSpinnerAnimation();
            }

            clearChat() {
                // 重置会话ID，下次发送消息时会创建新会话
                this.sessionId = null;

                this.chatOutput.innerHTML = `
                    <div class="message message-assistant">
                        <div class="timestamp">系统消息</div>
                        <div>对话已清空，下次发送消息将开始新会话</div>
                    </div>
                `;
            }

            async showHistory() {
                try {
                    const response = await fetch(`${this.baseURL}/api/history`);
                    const data = await response.json();

                    if (data.success && data.logs.length > 0) {
                        this.displayHistoryList(data.logs);
                    } else {
                        this.addMessage('system', '📚 暂无历史对话记录');
                    }
                } catch (error) {
                    this.addMessage('error', `❌ 获取历史记录失败: ${error.message}`);
                }
            }

            displayHistoryList(logs) {
                // 清空当前对话显示
                this.chatOutput.innerHTML = '';

                // 添加历史记录标题
                this.addMessage('system', `📚 历史对话记录 (共 ${logs.length} 条)`);

                // 显示每条历史记录
                logs.forEach((log, index) => {
                    const created = new Date(log.created).toLocaleString();
                    const modified = new Date(log.modified).toLocaleString();
                    const branchInfo = log.forkNumber ? ` (分支 #${log.forkNumber})` : '';
                    const sidechainInfo = log.sidechainNumber ? ` (侧链 #${log.sidechainNumber})` : '';

                    const historyDiv = document.createElement('div');
                    historyDiv.className = 'message message-tool';
                    historyDiv.style.cursor = 'pointer';
                    historyDiv.style.borderLeft = '4px solid #58a6ff';

                    historyDiv.innerHTML = `
                        <div class="timestamp">[${index}] ${created}</div>
                        <div>
                            <strong>${log.firstPrompt}</strong><br>
                            <small style="color: #7d8590;">
                                消息数: ${log.messageCount} | 修改: ${modified}${branchInfo}${sidechainInfo}
                            </small>
                        </div>
                    `;

                    // 添加点击事件，切换到历史对话
                    historyDiv.addEventListener('click', async () => {
                        await this.loadHistoryDetail(index, log);
                    });

                    this.chatOutput.appendChild(historyDiv);
                });

                // 添加返回按钮
                const backDiv = document.createElement('div');
                backDiv.className = 'message message-system';
                backDiv.innerHTML = `
                    <div class="timestamp">操作</div>
                    <div>
                        <button class="btn" onclick="location.reload()">返回对话</button>
                    </div>
                `;
                this.chatOutput.appendChild(backDiv);

                this.chatOutput.scrollTop = this.chatOutput.scrollHeight;
            }

            async loadHistoryDetail(index, logSummary) {
                try {
                    this.addMessage('system', `🔄 正在加载历史记录 [${index}]: ${logSummary.firstPrompt}`);

                    const response = await fetch(`${this.baseURL}/api/history/detail?index=${index}`);
                    const data = await response.json();

                    if (data.success) {
                        this.displayHistoryConversation(index, data.log, data.messages);
                    } else {
                        this.addMessage('error', `❌ 加载历史记录失败: ${data.error}`);
                    }
                } catch (error) {
                    this.addMessage('error', `❌ 加载历史记录失败: ${error.message}`);
                }
            }

            displayHistoryConversation(index, logInfo, messages) {
                // 清空当前显示
                this.chatOutput.innerHTML = '';

                // 添加历史记录标题
                const created = new Date(logInfo.created).toLocaleString();
                const modified = new Date(logInfo.modified).toLocaleString();
                const branchInfo = logInfo.forkNumber ? ` (分支 #${logInfo.forkNumber})` : '';
                const sidechainInfo = logInfo.sidechainNumber ? ` (侧链 #${logInfo.sidechainNumber})` : '';

                this.addMessage('system', `📚 历史对话 [${index}] - ${logInfo.firstPrompt}`);
                this.addMessage('system', `📅 创建: ${created} | 修改: ${modified} | 消息数: ${logInfo.messageCount}${branchInfo}${sidechainInfo}`);

                // 显示所有历史消息
                messages.forEach(msg => {
                    if (msg.type === 'user') {
                        // 用户消息
                        this.addMessage('user', this.extractTextContent(msg.content));
                    } else if (msg.type === 'assistant') {
                        // AI 响应
                        this.addMessage('assistant', this.extractTextContent(msg.content));
                    }
                });

                // 添加操作按钮
                const actionDiv = document.createElement('div');
                actionDiv.className = 'message message-system';
                actionDiv.innerHTML = `
                    <div class="timestamp">操作</div>
                    <div>
                        <button class="btn" onclick="client.resumeFromHistory(${index})">从此处继续对话</button>
                        <button class="clear-btn btn" onclick="client.showHistory()">返回历史列表</button>
                        <button class="clear-btn btn" onclick="location.reload()">返回当前对话</button>
                    </div>
                `;
                this.chatOutput.appendChild(actionDiv);

                this.chatOutput.scrollTop = this.chatOutput.scrollHeight;
            }

            extractTextContent(content) {
                if (typeof content === 'string') {
                    return content;
                }

                if (Array.isArray(content)) {
                    return content.map(block => {
                        if (block.type === 'text') {
                            return block.text;
                        } else if (block.type === 'tool_use') {
                            return `🔧 工具调用: ${block.name}\n参数: ${JSON.stringify(block.input, null, 2)}`;
                        } else if (block.type === 'tool_result') {
                            return `✅ 工具结果: ${block.content}`;
                        }
                        return JSON.stringify(block);
                    }).join('\n\n');
                }

                return JSON.stringify(content);
            }

            async resumeFromHistory(index) {
                try {
                    // 重置当前会话ID，这样下次发送消息时会创建新会话
                    this.sessionId = null;

                    // 清空当前对话
                    this.chatOutput.innerHTML = `
                        <div class="message message-assistant">
                            <div class="timestamp">系统消息</div>
                            <div>✅ 已从历史记录 [${index}] 恢复对话上下文，可以继续对话了</div>
                        </div>
                    `;

                    this.addMessage('system', '💡 提示: 你现在可以基于之前的对话内容继续提问');

                } catch (error) {
                    this.addMessage('error', `❌ 恢复对话失败: ${error.message}`);
                }
            }
        }

        // 初始化客户端
        const client = new KodeAPIClient();
    </script>
</body>
</html>
