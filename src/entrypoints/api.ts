#!/usr/bin/env node
import { createServer, IncomingMessage, ServerResponse } from 'http'
import { URL } from 'url'
import { setCwd, setOriginalCwd } from '../utils/state'
import { getTools } from '../tools'
import { getCommands } from '../commands'
import { hasPermissionsToUseTool } from '../permissions'
import { ask } from '../utils/ask'
import { dateToFilename } from '../utils/log'
import { enableConfigs } from '../utils/config'
import { initSentry } from '../services/sentry'
import { addToHistory } from '../history'
import { initDebugLogger } from '../utils/debugLogger'

// 初始化系统
initSentry()
initDebugLogger()

interface ApiServerConfig {
  port: number
  host: string
  cwd?: string
  enableArchitect?: boolean
  safeMode?: boolean
  verbose?: boolean
  env?: Record<string, string>
}

interface ChatRequest {
  message: string
  enableArchitect?: boolean
  safeMode?: boolean
  sessionId?: string
}

interface ChatResponse {
  success: boolean
  data?: {
    response: string
    sessionId: string
  }
  error?: string
}

class KodeApiServer {
  private server: ReturnType<typeof createServer>
  private config: ApiServerConfig
  private activeSessions = new Map<string, {
    tools: any[],
    commands: any[],
    statusMessage: string,
    messageHistory: string[],
    createdAt: number
  }>()

  constructor(config: ApiServerConfig) {
    this.config = config
    this.server = createServer(this.handleRequest.bind(this))

    // 定期清理过期会话（30分钟）
    setInterval(() => {
      const now = Date.now()
      const expiredSessions = []
      for (const [sessionId, session] of this.activeSessions.entries()) {
        if (now - session.createdAt > 30 * 60 * 1000) { // 30分钟
          expiredSessions.push(sessionId)
        }
      }
      for (const sessionId of expiredSessions) {
        this.activeSessions.delete(sessionId)
        console.log(`🗑️ 清理过期会话: ${sessionId}`)
      }
    }, 5 * 60 * 1000) // 每5分钟检查一次
  }

  // 在服务器启动时初始化环境
  private async initialize() {
    const result = await this.setupEnvironment(this.config.cwd, this.config.env)
    if (!result.success) {
      throw new Error(`初始化失败: ${result.error}`)
    }
    return result
  }

  private async setupEnvironment(cwd?: string, env?: Record<string, string>) {
    try {
      enableConfigs()
      
      // 设置环境变量
      if (env) {
        Object.entries(env).forEach(([key, value]) => {
          process.env[key] = value
        })
      }

      // 设置工作目录
      const workingDir = cwd || this.config.cwd || process.cwd()
      if (workingDir !== process.cwd()) {
        setOriginalCwd(workingDir)
      }
      await setCwd(workingDir)

      // 初始化模型管理器 - 确保模型配置已加载
      const { getModelManager } = await import('../utils/model')
      const modelManager = getModelManager()
      
      // 验证主模型是否可用
      const mainModel = modelManager.getMainAgentModel()
      if (!mainModel) {
        throw new Error('未找到可用的主模型，请先配置模型')
      }

      return { success: true, mainModel }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      }
    }
  }

  private async handleRequest(req: IncomingMessage, res: ServerResponse) {
    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', '*')
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept, Cache-Control, DNT, User-Agent, X-Requested-With, If-Modified-Since, If-None-Match, sec-ch-ua, sec-ch-ua-mobile, sec-ch-ua-platform')
    res.setHeader('Access-Control-Allow-Credentials', 'false')

    if (req.method === 'OPTIONS') {
      res.writeHead(200)
      res.end()
      return
    }

    const url = new URL(req.url || '', `http://${req.headers.host}`)
    const pathname = url.pathname

    try {
      switch (pathname) {
        case '/api/start':
          await this.handleStart(req, res)
          break
        case '/api/stop':
          await this.handleStop(req, res)
          break
        case '/api/chat':
          await this.handleChat(req, res)
          break
        case '/api/chat/stream':
          await this.handleChatStream(req, res)
          break
        case '/api/health':
          this.handleHealth(req, res)
          break
        case '/api/history':
          await this.handleHistory(req, res)
          break
        case '/api/history/detail':
          await this.handleHistoryDetail(req, res)
          break
        case '/api/test':
          await this.handleTest(req, res)
          break
        default:
          this.send404(res)
      }
    } catch (error) {
      this.sendError(res, error instanceof Error ? error.message : String(error))
    }
  }

  private async handleStart(req: IncomingMessage, res: ServerResponse) {
    if (req.method !== 'POST') {
      res.writeHead(405)
      res.end('Method not allowed')
      return
    }

    const body = await this.getRequestBody(req)
    const { cwd, env } = JSON.parse(body) as { cwd?: string; env?: Record<string, string> }

    console.log(`🚀 收到项目启动请求`)
    console.log(`📂 请求的工作目录: ${cwd}`)

    const result = await this.setupEnvironment(cwd, env)

    if (result.success) {
      const currentCwd = await import('../utils/state').then(m => m.getCwd())
      console.log(`✅ 项目启动成功！`)
      console.log(`📁 工作目录已设置: ${currentCwd}`)
      console.log(`🎯 项目已就绪，等待对话请求...`)

      this.sendJson(res, {
        success: true,
        message: '服务启动成功',
        cwd: currentCwd
      })
    } else {
      console.log(`❌ 项目启动失败: ${result.error}`)
      this.sendError(res, result.error || '服务启动失败')
    }
  }

  private async handleStop(req: IncomingMessage, res: ServerResponse) {
    if (req.method !== 'POST') {
      res.writeHead(405)
      res.end('Method not allowed')
      return
    }

    console.log(`🛑 收到项目停止请求`)

    try {
      // 重置工作目录到默认目录
      const { setCwd } = await import('../utils/state')
      await setCwd(process.cwd())

      console.log(`✅ 项目已停止`)
      console.log(`📁 工作目录已重置: ${process.cwd()}`)
      console.log(`💤 等待新的项目启动请求...`)

      this.sendJson(res, {
        success: true,
        message: '项目已停止',
        cwd: process.cwd()
      })
    } catch (error) {
      console.log(`❌ 项目停止失败: ${error instanceof Error ? error.message : String(error)}`)
      this.sendError(res, error instanceof Error ? error.message : '项目停止失败')
    }
  }

  private async handleChat(req: IncomingMessage, res: ServerResponse) {
    if (req.method !== 'POST') {
      res.writeHead(405)
      res.end('Method not allowed')
      return
    }

    const body = await this.getRequestBody(req)
    const chatRequest = JSON.parse(body) as ChatRequest

    try {
      console.log('🔄 开始处理对话请求:', chatRequest.message)

      // 获取工具和命令
      const [tools, commands] = await Promise.all([
        getTools(chatRequest.enableArchitect ?? this.config.enableArchitect),
        getCommands(),
      ])

      console.log(`✅ 工具和命令加载完成: ${tools.length} 工具, ${commands.length} 命令`)

      // 添加到历史记录
      addToHistory(chatRequest.message)

      console.log('🤖 开始调用AI模型...')

      // 使用框架的ask函数处理所有请求
      const { resultText } = await ask({
        commands,
        hasPermissionsToUseTool,
        messageLogName: dateToFilename(new Date()),
        prompt: chatRequest.message,
        cwd: await import('../utils/state').then(m => m.getCwd()),
        tools,
        safeMode: chatRequest.safeMode ?? this.config.safeMode,
      })
      const response = resultText

      console.log('✅ AI响应完成')

      const chatResponse: ChatResponse = {
        success: true,
        data: {
          response,
          sessionId: chatRequest.sessionId || this.generateSessionId(),
        }
      }

      this.sendJson(res, chatResponse)
    } catch (error) {
      console.error('❌ 对话处理失败:', error)
      const errorResponse: ChatResponse = {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
      this.sendJson(res, errorResponse)
    }
  }

  private async handleChatStream(req: IncomingMessage, res: ServerResponse) {
    if (req.method !== 'POST') {
      res.writeHead(405)
      res.end('Method not allowed')
      return
    }

    // 设置SSE头
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
    })

    const body = await this.getRequestBody(req)
    const chatRequest = JSON.parse(body) as ChatRequest

    try {
      // 发送开始事件
      const sessionId = chatRequest.sessionId || this.generateSessionId()
      this.sendSSEEvent(res, 'start', { sessionId })

      // 获取或创建会话
      let session = this.activeSessions.get(sessionId)

      if (!session) {
        // 新会话：获取工具和命令，选择状态消息
        const [tools, commands] = await Promise.all([
          getTools(chatRequest.enableArchitect ?? this.config.enableArchitect),
          getCommands(),
        ])

        const { sample } = await import('lodash-es')
        const statusMessages = [
          'Accomplishing', 'Actioning', 'Actualizing', 'Baking', 'Brewing',
          'Calculating', 'Cerebrating', 'Churning', 'Coding', 'Coalescing',
          'Cogitating', 'Computing', 'Conjuring', 'Considering', 'Cooking',
          'Crafting', 'Creating', 'Crunching', 'Deliberating', 'Determining',
          'Doing', 'Effecting', 'Finagling', 'Forging', 'Forming',
          'Generating', 'Hatching', 'Herding', 'Honking', 'Hustling',
          'Ideating', 'Inferring', 'Manifesting', 'Marinating', 'Moseying',
          'Mulling', 'Mustering', 'Musing', 'Noodling', 'Percolating',
          'Pondering', 'Processing', 'Puttering', 'Reticulating', 'Ruminating',
          'Schlepping', 'Shucking', 'Simmering', 'Smooshing', 'Spinning',
          'Stewing', 'Synthesizing', 'Thinking', 'Transmuting', 'Vibing', 'Working'
        ]

        session = {
          tools,
          commands,
          statusMessage: sample(statusMessages) || 'Processing',
          messageHistory: [],
          createdAt: Date.now()
        }

        this.activeSessions.set(sessionId, session)
        console.log(`🆕 创建新会话: ${sessionId}, 状态消息: ${session.statusMessage}`)
      } else {
        console.log(`🔄 继续会话: ${sessionId}, 状态消息: ${session.statusMessage}`)
      }

      // 添加到会话历史记录
      session.messageHistory.push(chatRequest.message)
      addToHistory(chatRequest.message)

      // 发送状态指示器（使用会话的固定状态消息）
      this.sendSSEEvent(res, 'status', {
        message: session.statusMessage,
        startTime: Date.now()
      })

      // 使用真正的流式处理 - 像命令行模式一样显示每个步骤
      const { query } = await import('../query')
      const { getSystemPrompt } = await import('../constants/prompts')
      const { getContext } = await import('../context')
      const { createUserMessage } = await import('../utils/messages')
      const { formatSystemPromptWithContext } = await import('../services/claude')

      // 创建消息，包含会话历史
      const message = createUserMessage(chatRequest.message)

      // 构建包含历史的消息数组
      const messages = []

      // 添加会话历史（除了当前消息）
      if (session.messageHistory.length > 1) {
        for (let i = 0; i < session.messageHistory.length - 1; i++) {
          messages.push(createUserMessage(session.messageHistory[i]))
        }
      }

      // 添加当前消息
      messages.push(message)

      // 获取系统提示和上下文
      const [systemPrompt, context] = await Promise.all([
        getSystemPrompt(),
        getContext(),
      ])

      // 格式化系统提示
      const systemPromptArray = Array.isArray(systemPrompt) ? systemPrompt : [systemPrompt]
      const { systemPrompt: fullSystemPrompt } = formatSystemPromptWithContext(systemPromptArray, context)

      // 创建工具使用上下文
      const toolUseContext = {
        abortController: new AbortController(),
        options: {
          commands: session.commands,
          tools: session.tools,
          verbose: true, // 启用详细模式以获得更多输出
          safeMode: chatRequest.safeMode ?? this.config.safeMode,
          forkNumber: 0,
          messageLogName: dateToFilename(new Date()),
          maxThinkingTokens: 0,
        },
        messageId: undefined,
        readFileTimestamps: {},
        setToolJSX: (jsx: any) => {
          // 当工具执行时，发送工具状态
          if (jsx && jsx.props) {
            const toolName = jsx.type?.name || 'Tool'
            const status = jsx.props.status || 'running'
            this.sendSSEEvent(res, 'tool', {
              tool: toolName,
              status: status,
              data: jsx.props
            })
          }
        },
      }

      let fullResponse = ''
      let messageCount = 0
      const startTime = Date.now()

      // 启动状态更新定时器
      const statusInterval = setInterval(() => {
        const elapsedSeconds = Math.floor((Date.now() - startTime) / 1000)
        this.sendSSEEvent(res, 'status_update', {
          message: session.statusMessage,
          elapsedTime: elapsedSeconds
        })
      }, 1000)

      try {
        // 真正的流式处理 - 逐步显示每个消息
        for await (const messageChunk of query(
          messages,
          fullSystemPrompt,
          context,
          hasPermissionsToUseTool,
          toolUseContext,
        )) {
        messageCount++

        if (messageChunk.type === 'assistant') {
          // 处理助手消息
          const content = messageChunk.message.content
          if (content && content.length > 0) {
            for (const block of content) {
              if (block.type === 'text') {
                const text = block.text
                this.sendSSEEvent(res, 'chunk', {
                  data: text,
                  messageIndex: messageCount,
                  type: 'assistant_text'
                })
                fullResponse += text
              } else if (block.type === 'tool_use') {
                // 发送工具调用信息
                this.sendSSEEvent(res, 'tool_call', {
                  tool: block.name,
                  input: block.input,
                  id: block.id,
                  messageIndex: messageCount
                })
              }
            }
          }
        } else if (messageChunk.type === 'user') {
          // 处理用户消息（包括工具结果）
          const content = messageChunk.message.content
          if (Array.isArray(content)) {
            for (const block of content) {
              if (block.type === 'tool_result') {
                this.sendSSEEvent(res, 'tool_result', {
                  tool_use_id: block.tool_use_id,
                  content: block.content,
                  is_error: block.is_error,
                  messageIndex: messageCount
                })
              }
            }
          }
        } else if (messageChunk.type === 'progress') {
          // 处理进度消息
          this.sendSSEEvent(res, 'progress', {
            content: messageChunk.content,
            messageIndex: messageCount
          })
        }
        }

        // 发送最终结果
        this.sendSSEEvent(res, 'complete', { response: fullResponse })

      } finally {
        // 清理状态更新定时器
        clearInterval(statusInterval)
      }

    } catch (error) {
      this.sendSSEEvent(res, 'error', {
        error: error instanceof Error ? error.message : String(error)
      })
    } finally {
      this.sendSSEEvent(res, 'end', {})
      res.end()
    }
  }

  private async handleTest(req: IncomingMessage, res: ServerResponse) {
    try {
      // 测试模型配置
      const { getModelManager } = await import('../utils/model')
      const modelManager = getModelManager()
      const mainModel = modelManager.getMainAgentModel()
      
      // 测试工具和命令加载
      const [tools, commands] = await Promise.all([
        getTools(false),
        getCommands(),
      ])

      this.sendJson(res, {
        success: true,
        data: {
          model: mainModel,
          toolsCount: tools.length,
          commandsCount: commands.length,
          cwd: await import('../utils/state').then(m => m.getCwd()),
        }
      })
    } catch (error) {
      this.sendJson(res, {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  private handleHealth(req: IncomingMessage, res: ServerResponse) {
    this.sendJson(res, {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0'
    })
  }

  private async handleHistory(req: IncomingMessage, res: ServerResponse) {
    if (req.method !== 'GET') {
      res.writeHead(405)
      res.end('Method not allowed')
      return
    }

    try {
      const { loadLogList, CACHE_PATHS } = await import('../utils/log')
      const logs = await loadLogList(CACHE_PATHS.messages())

      // 格式化日志数据，只返回必要信息
      const formattedLogs = logs.map(log => ({
        date: log.date,
        created: log.created,
        modified: log.modified,
        firstPrompt: log.firstPrompt,
        messageCount: log.messageCount,
        forkNumber: log.forkNumber,
        sidechainNumber: log.sidechainNumber,
        value: log.value
      }))

      this.sendJson(res, {
        success: true,
        logs: formattedLogs,
        total: formattedLogs.length
      })
    } catch (error) {
      this.sendJson(res, {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        logs: [],
        total: 0
      })
    }
  }

  private async handleHistoryDetail(req: IncomingMessage, res: ServerResponse) {
    if (req.method !== 'GET') {
      res.writeHead(405)
      res.end('Method not allowed')
      return
    }

    try {
      const url = new URL(req.url || '', `http://${req.headers.host}`)
      const logIndex = parseInt(url.searchParams.get('index') || '0')

      const { loadLogList, CACHE_PATHS } = await import('../utils/log')
      const logs = await loadLogList(CACHE_PATHS.messages())

      if (logIndex < 0 || logIndex >= logs.length) {
        this.sendJson(res, {
          success: false,
          error: '历史记录索引无效',
          messages: []
        })
        return
      }

      const log = logs[logIndex]

      // 格式化消息数据，转换为适合前端显示的格式
      const formattedMessages = log.messages.map(msg => ({
        type: msg.type,
        content: msg.message?.content || msg.content,
        timestamp: msg.timestamp,
        role: msg.type === 'user' ? 'user' : 'assistant'
      }))

      this.sendJson(res, {
        success: true,
        log: {
          date: log.date,
          created: log.created,
          modified: log.modified,
          firstPrompt: log.firstPrompt,
          messageCount: log.messageCount,
          forkNumber: log.forkNumber,
          sidechainNumber: log.sidechainNumber
        },
        messages: formattedMessages
      })
    } catch (error) {
      this.sendJson(res, {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        messages: []
      })
    }
  }

  private sendSSEEvent(res: ServerResponse, event: string, data: any) {
    res.write(`event: ${event}\n`)
    res.write(`data: ${JSON.stringify(data)}\n\n`)
  }

  private sendJson(res: ServerResponse, data: any) {
    res.setHeader('Content-Type', 'application/json')
    res.writeHead(200)
    res.end(JSON.stringify(data))
  }

  private sendError(res: ServerResponse, message: string, status = 500) {
    res.setHeader('Content-Type', 'application/json')
    res.writeHead(status)
    res.end(JSON.stringify({ success: false, error: message }))
  }

  private send404(res: ServerResponse) {
    res.writeHead(404)
    res.end('Not Found')
  }

  private async getRequestBody(req: IncomingMessage): Promise<string> {
    return new Promise((resolve, reject) => {
      let body = ''
      req.on('data', chunk => {
        body += chunk.toString()
      })
      req.on('end', () => {
        resolve(body)
      })
      req.on('error', reject)
    })
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  public async start(): Promise<void> {
    // 先初始化环境
    try {
      const initResult = await this.initialize()
      console.log(`✅ 环境初始化成功，主模型: ${initResult.mainModel}`)
      console.log(`  工作目录: ${await import('../utils/state').then(m => m.getCwd())}`)
    } catch (error) {
      console.error('❌ 环境初始化失败:', error)
      throw error
    }

    return new Promise((resolve, reject) => {
      this.server.listen(this.config.port, this.config.host, () => {
        console.log(`🚀 Kode API服务已启动`)
        console.log(`📍 地址: http://${this.config.host}:${this.config.port}`)
        console.log(`📁 默认工作目录: ${process.cwd()}`)
        console.log(`💡 使用 POST /api/start 设置项目工作目录`)
        console.log('')
        console.log('可用的API端点:')
        console.log(`  POST /api/start    - 启动项目设置`)
        console.log(`  POST /api/stop     - 停止项目`)
        console.log(`  POST /api/chat     - 普通对话`)
        console.log(`  POST /api/chat/stream - SSE流式对话`)
        console.log(`  GET  /api/history  - 查看对话历史`)
        console.log(`  GET  /api/history/detail?index=N - 查看特定历史记录详情`)
        console.log(`  GET  /api/health   - 健康检查`)
        resolve()
      })

      this.server.on('error', reject)
    })
  }

  public stop(): Promise<void> {
    return new Promise((resolve) => {
      this.server.close(() => {
        console.log('Kode API服务已停止')
        resolve()
      })
    })
  }
}

// 启动API服务器的函数
export async function startApiServer(config: Partial<ApiServerConfig> = {}): Promise<KodeApiServer> {
  const defaultConfig: ApiServerConfig = {
    port: 3000,
    host: '0.0.0.0',
    cwd: process.cwd(),
    enableArchitect: false,
    safeMode: false,
    verbose: false,
    ...config
  }

  const server = new KodeApiServer(defaultConfig)
  await server.start()
  return server
}

// 如果直接运行此文件，启动服务器
if (require.main === module) {
  const config: Partial<ApiServerConfig> = {
    port: parseInt(process.env.PORT || '3000'),
    host: process.env.HOST || '0.0.0.0',
    cwd: process.env.CWD || process.cwd(),
    enableArchitect: process.env.ENABLE_ARCHITECT === 'true',
    safeMode: process.env.SAFE_MODE === 'true',
    verbose: process.env.VERBOSE === 'true',
  }

  startApiServer(config).catch(error => {
    console.error('启动API服务器失败:', error)
    process.exit(1)
  })

  // 优雅关闭
  process.on('SIGINT', async () => {
    console.log('\n正在关闭服务器...')
    process.exit(0)
  })

  process.on('SIGTERM', async () => {
    console.log('\n正在关闭服务器...')
    process.exit(0)
  })
}